apiVersion: v1
data:
    env.json: |
        {
          "API_URL": "https://deploy-prod.evdi.app/api",
          "DEPLOYMENT_TYPE": "EMPE_OVH_K8S_DEPLOYMENT",
          "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": "pk_live_51RWgpdFRtYRiDObJKqJDYmsj8u5XCThcsL4D5BOKcRZQ5tLpYnzl9dM9R6I9nOesfHEpYaJSQmG3Ddo6Zon8QVR600uz9T3x66",
          "NEXT_PUBLIC_STRIPE_PRICING_TABLE_ID": "prctbl_1RqbW0FRtYRiDObJaHwFe5c4"
        }
kind: ConfigMap
metadata:
    name: app-config-one-click-deployer
    namespace: evdi-production-one-click-deployment
