'use client';
import { useUserStore } from '@/store/userStore';
import { useEffect, useState } from 'react';
import axios from 'axios';
import Script from 'next/script';

export const ZendeskChat = () => {
    const { user } = useUserStore();
    const [isZendeskLoaded, setIsZendeskLoaded] = useState(false);

    useEffect(() => {
        const identifyUser = async () => {
            if (!user || !user.id || !user.email) {
                console.warn('User data incomplete:', user);
                return;
            }

            try {
                const { data } = await axios.post('/api/zendesk-token', {
                    id: user.id,
                    name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'Unknown',
                    email: user.email,
                });

                const token = data.token;

                const waitForZendesk = (attempts = 50, interval = 100) => {
                    if (typeof window.zE !== 'function') {
                        if (attempts <= 0) {
                            console.error('Zendesk script failed to load');
                            return;
                        }
                        setTimeout(() => waitForZendesk(attempts - 1, interval), interval);
                        return;
                    }

                    window.zE('messenger', 'loginUser', function (cb: any) {
                        cb(token);
                    });
                };

                waitForZendesk();
            } catch (error) {
                console.error('Failed to identify user in Zendesk:', error);
            }
        };

        if (user && isZendeskLoaded) {
            identifyUser();
        }
    }, [user, isZendeskLoaded]);

    return (
        <Script
            id="ze-snippet"
            src="https://static.zdassets.com/ekr/snippet.js?key=bbb92d84-74f1-41d0-a90d-74bd558bbc72"
            strategy="afterInteractive"
            onLoad={() => setIsZendeskLoaded(true)}
        />
    );
};
